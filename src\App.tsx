import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import AuthLayout from './components/layouts/AuthLayout';
import DashboardLayout from './components/layouts/DashboardLayout';
import Loading from './components/ui/Loading';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './components/ui/NotificationSystem';

// Lazy load pages - النظام الموحد الجديد
const Login = lazy(() => import('./pages/auth/Login'));
const SystemSetup = lazy(() => import('./pages/setup/SystemSetup'));
const Dashboard = lazy(() => import('./pages/dashboard/Dashboard'));
const StrategicObjectives = lazy(() => import('./pages/strategic/StrategicObjectives'));
const Initiatives = lazy(() => import('./pages/initiatives/Initiatives'));
const KPIs = lazy(() => import('./pages/kpis/KPIs'));
const Activities = lazy(() => import('./pages/activities/Activities'));
const Programs = lazy(() => import('./pages/programs/Programs'));
const OperationalPlan = lazy(() => import('./pages/operational-plan/OperationalPlan'));
const Users = lazy(() => import('./pages/users/Users'));
const Departments = lazy(() => import('./pages/departments/Departments'));
const Reports = lazy(() => import('./pages/reports/Reports'));
const SmartImportPage = lazy(() => import('./pages/smart-import/SmartImportPage'));
const DeleteDataPage = lazy(() => import('./pages/delete-data/DeleteDataPage'));
const CreateAuthAccounts = lazy(() => import('./pages/admin/CreateAuthAccounts'));
const UserCredentials = lazy(() => import('./pages/admin/UserCredentials'));
const TestLogin = lazy(() => import('./pages/admin/TestLogin'));
const NotFound = lazy(() => import('./pages/errors/NotFound'));

function App() {
  return (
    <Router>
      <AuthProvider>
        <NotificationProvider>
          <Suspense fallback={<Loading />}>
            <Routes>
              {/* Setup Route */}
              <Route path="/setup" element={<SystemSetup />} />

              {/* Auth Routes */}
              <Route element={<AuthLayout />}>
                <Route path="/login" element={<Login />} />
              </Route>

              {/* Protected Dashboard Routes - النظام الموحد الجديد */}
              <Route element={<DashboardLayout />}>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/strategic-objectives" element={<StrategicObjectives />} />
                <Route path="/initiatives" element={<Initiatives />} />
                <Route path="/kpis" element={<KPIs />} />
                <Route path="/activities" element={<Activities />} />
                <Route path="/programs" element={<Programs />} />
                <Route path="/operational-plan" element={<OperationalPlan />} />
                <Route path="/users" element={<Users />} />
                <Route path="/departments" element={<Departments />} />
                <Route path="/reports" element={<Reports />} />
                <Route path="/smart-import" element={<SmartImportPage />} />
                <Route path="/delete-data" element={<DeleteDataPage />} />
                <Route path="/admin/create-auth-accounts" element={<CreateAuthAccounts />} />
                <Route path="/admin/user-credentials" element={<UserCredentials />} />
                <Route path="/admin/test-login" element={<TestLogin />} />
              </Route>

              {/* 404 Not Found */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </NotificationProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;