import { useState, useEffect } from 'react';
import { UserService } from '../../lib/userService';
import { useNotification } from '../../components/ui/NotificationSystem';
import { Copy, Eye, EyeOff } from 'lucide-react';

const UserCredentials = () => {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPasswords, setShowPasswords] = useState(false);
  const { showNotification } = useNotification();

  const defaultPassword = 'Strategic@123';

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      const allUsers = await UserService.getAllUsers();
      // فلترة المستخدمين الذين لديهم حسابات نظام
      const systemUsers = allUsers.filter(user => 
        user.has_system_account && 
        user.email && 
        user.email !== '<EMAIL>'
      );
      setUsers(systemUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      showNotification('خطأ في جلب المستخدمين', 'error');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showNotification(`تم نسخ ${label}`, 'success');
    } catch (error) {
      showNotification('فشل في النسخ', 'error');
    }
  };

  const copyAllCredentials = async () => {
    const credentialsText = users.map(user => 
      `${user.name}\nالبريد: ${user.email}\nكلمة المرور: ${defaultPassword}\nالمنصب: ${user.role}\nالقسم: ${user.department}\n---`
    ).join('\n');

    try {
      await navigator.clipboard.writeText(credentialsText);
      showNotification('تم نسخ جميع بيانات الدخول', 'success');
    } catch (error) {
      showNotification('فشل في النسخ', 'error');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-neutral-600">جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-neutral-900">
            بيانات تسجيل الدخول للمستخدمين
          </h1>
          
          <div className="flex gap-3">
            <button
              onClick={() => setShowPasswords(!showPasswords)}
              className="btn-secondary flex items-center gap-2"
            >
              {showPasswords ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {showPasswords ? 'إخفاء كلمات المرور' : 'إظهار كلمات المرور'}
            </button>
            
            <button
              onClick={copyAllCredentials}
              className="btn-primary flex items-center gap-2"
            >
              <Copy className="w-4 h-4" />
              نسخ جميع البيانات
            </button>
          </div>
        </div>

        <div className="grid gap-4">
          {users.map((user) => (
            <div
              key={user.id}
              className="bg-neutral-50 rounded-lg p-4 border border-neutral-200"
            >
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-neutral-900 text-lg mb-2">
                    {user.name}
                  </h3>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-neutral-700">البريد الإلكتروني:</span>
                      <span className="text-neutral-600">{user.email}</span>
                      <button
                        onClick={() => copyToClipboard(user.email, 'البريد الإلكتروني')}
                        className="text-primary-600 hover:text-primary-700"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-neutral-700">كلمة المرور:</span>
                      <span className="text-neutral-600 font-mono">
                        {showPasswords ? defaultPassword : '••••••••••••'}
                      </span>
                      <button
                        onClick={() => copyToClipboard(defaultPassword, 'كلمة المرور')}
                        className="text-primary-600 hover:text-primary-700"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium text-neutral-700">المنصب:</span>
                    <span className="text-neutral-600 mr-2">{user.role}</span>
                  </div>
                  
                  <div>
                    <span className="font-medium text-neutral-700">القسم:</span>
                    <span className="text-neutral-600 mr-2">{user.department || 'غير محدد'}</span>
                  </div>
                  
                  {user.phone && (
                    <div>
                      <span className="font-medium text-neutral-700">الهاتف:</span>
                      <span className="text-neutral-600 mr-2">{user.phone}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">ملاحظات مهمة:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• كلمة المرور الموحدة لجميع المستخدمين: <strong>Strategic@123</strong></li>
            <li>• يمكن للمستخدمين تغيير كلمة المرور بعد تسجيل الدخول</li>
            <li>• تأكد من إبلاغ كل مستخدم ببيانات الدخول الخاصة به</li>
            <li>• احتفظ بهذه البيانات في مكان آمن</li>
          </ul>
        </div>

        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h4 className="font-semibold text-green-900 mb-2">حالة النظام:</h4>
          <p className="text-sm text-green-800">
            ✅ تم إعادة تعيين كلمات المرور لجميع المستخدمين بنجاح
          </p>
          <p className="text-sm text-green-800">
            ✅ جميع الحسابات جاهزة لتسجيل الدخول
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserCredentials;
