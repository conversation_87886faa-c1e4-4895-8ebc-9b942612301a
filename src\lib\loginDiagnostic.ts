// ====================================
// أداة تشخيص مشاكل تسجيل الدخول
// ====================================

import { supabase } from './supabase';

// 1. فحص المستخدمين في Auth مقابل Users table
export const diagnoseMismatch = async () => {
  console.log('🔍 بدء تشخيص مشكلة تسجيل الدخول...')
  
  try {
    // فحص المستخدمين في Auth (يتطلب Service Role Key)
    console.log('⚠️ ملاحظة: هذا يتطلب Service Role Key')
    
    // الطريقة البديلة: فحص Users table مباشرة
    const { data: usersInTable, error: usersError } = await supabase
      .from('users')
      .select('id, email, name, created_at')
    
    if (usersError) {
      console.error('❌ خطأ في قراءة جدول users:', usersError)
      return {
        success: false,
        error: 'لا يمكن قراءة جدول المستخدمين: ' + usersError.message,
        suggestions: [
          'تحقق من سياسات RLS',
          'تأكد من وجود سياسة SELECT للمستخدمين',
          'راجع إعدادات قاعدة البيانات'
        ]
      }
    }

    console.log('📊 المستخدمين في جدول users:', usersInTable)

    if (!usersInTable || usersInTable.length === 0) {
      return {
        success: false,
        issue: 'NO_USERS_IN_TABLE',
        error: 'لا يوجد مستخدمين في جدول users',
        suggestions: [
          'أضف المستخدمين إلى جدول users',
          'تأكد من تنفيذ SQL setup كاملاً',
          'راجع عملية إنشاء المستخدمين'
        ]
      }
    }

    return {
      success: true,
      usersCount: usersInTable.length,
      users: usersInTable
    }

  } catch (error: any) {
    console.error('❌ خطأ في التشخيص:', error)
    return {
      success: false,
      error: 'خطأ في تشخيص المستخدمين: ' + error.message
    }
  }
}

// 2. اختبار تسجيل الدخول مع مستخدم محدد
export const testLogin = async (email: string, password: string) => {
  console.log(`🧪 اختبار تسجيل الدخول لـ: ${email}`)
  
  try {
    // محاولة تسجيل الدخول
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (authError) {
      console.error('❌ خطأ في المصادقة:', authError)
      
      // تحليل نوع الخطأ
      if (authError.message.includes('Invalid login credentials')) {
        return {
          success: false,
          issue: 'INVALID_CREDENTIALS',
          error: 'بيانات الدخول غير صحيحة',
          suggestions: [
            'تحقق من البريد الإلكتروني وكلمة المرور',
            'تأكد من أن المستخدم موجود في Auth',
            'تحقق من تأكيد البريد الإلكتروني'
          ]
        }
      } else if (authError.message.includes('Email not confirmed')) {
        return {
          success: false,
          issue: 'EMAIL_NOT_CONFIRMED',
          error: 'البريد الإلكتروني غير مؤكد',
          suggestions: [
            'أكّد البريد الإلكتروني',
            'أو فعّل auto-confirm في Supabase',
            'تحقق من إعدادات Auth'
          ]
        }
      } else {
        return {
          success: false,
          issue: 'AUTH_ERROR',
          error: 'خطأ في المصادقة: ' + authError.message,
          suggestions: [
            'تحقق من إعدادات Supabase',
            'راجع مفاتيح API',
            'تأكد من إعدادات Auth'
          ]
        }
      }
    }

    console.log('✅ نجحت المصادقة، المستخدم:', authData.user.id)

    // الآن نحاول جلب بيانات المستخدم من جدول users
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('auth_id', authData.user.id)
      .single()

    if (userError) {
      console.error('❌ خطأ في جلب بيانات المستخدم:', userError)
      
      if (userError.code === 'PGRST116') {
        return {
          success: false,
          issue: 'USER_NOT_IN_TABLE',
          error: 'المستخدم موجود في Auth لكن غير موجود في جدول users',
          authUser: authData.user,
          suggestions: [
            'أضف المستخدم إلى جدول users:',
            `INSERT INTO public.users (auth_id, email, name) VALUES ('${authData.user.id}', '${authData.user.email}', '${authData.user.email}');`,
            'أو راجع عملية إنشاء المستخدمين'
          ]
        }
      } else {
        return {
          success: false,
          issue: 'RLS_POLICY_ERROR',
          error: 'مشكلة في سياسات الأمان (RLS)',
          suggestions: [
            'تحقق من سياسات SELECT في جدول users',
            'تأكد من أن السياسة تسمح بـ auth.uid() = auth_id',
            'راجع إعدادات RLS'
          ]
        }
      }
    }

    console.log('✅ تم جلب بيانات المستخدم بنجاح:', userData)

    // تسجيل الخروج بعد الاختبار
    await supabase.auth.signOut()

    return {
      success: true,
      message: 'تسجيل الدخول يعمل بشكل صحيح!',
      authUser: authData.user,
      userData: userData
    }

  } catch (error: any) {
    console.error('❌ خطأ عام في اختبار تسجيل الدخول:', error)
    return {
      success: false,
      issue: 'GENERAL_ERROR',
      error: 'خطأ عام: ' + error.message
    }
  }
}

// 3. فحص سياسات RLS
export const checkRLSPolicies = async () => {
  console.log('🔍 فحص سياسات RLS...')
  
  try {
    // محاولة قراءة المستخدمين بدون مصادقة (يجب أن تفشل)
    const { data, error } = await supabase
      .from('users')
      .select('count(*)')
      
    if (error) {
      console.log('✅ RLS يعمل بشكل صحيح (منع الوصول بدون مصادقة)')
      return {
        rlsEnabled: true,
        working: true
      }
    } else {
      console.warn('⚠️ RLS قد لا يعمل بشكل صحيح')
      return {
        rlsEnabled: false,
        working: false,
        warning: 'يمكن الوصول للبيانات بدون مصادقة'
      }
    }
  } catch (error: any) {
    return {
      error: 'خطأ في فحص RLS: ' + error.message
    }
  }
}

// 4. اختبار شامل
export const runFullLoginDiagnostic = async () => {
  console.log('🚀 بدء التشخيص الشامل لمشاكل تسجيل الدخول...')
  
  const results: any = {}
  
  // 1. فحص المستخدمين
  console.log('\n1️⃣ فحص المستخدمين في قاعدة البيانات...')
  results.users = await diagnoseMismatch()
  
  // 2. فحص RLS
  console.log('\n2️⃣ فحص سياسات الأمان...')
  results.rls = await checkRLSPolicies()
  
  // 3. اختبار تسجيل الدخول مع الحسابات التجريبية
  console.log('\n3️⃣ اختبار تسجيل الدخول مع الحسابات التجريبية...')
  
  const testAccounts = [
    { email: '<EMAIL>', password: 'shaker1420' },
    { email: '<EMAIL>', password: 'hemam2025' },
    { email: '<EMAIL>', password: 'hemam2025' }
  ]
  
  results.loginTests = []
  
  for (const account of testAccounts) {
    const testResult = await testLogin(account.email, account.password)
    results.loginTests.push({
      email: account.email,
      result: testResult
    })
    
    // انتظار قصير بين الاختبارات
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  // تحليل النتائج
  const analysis = analyzeLoginResults(results)
  
  console.log('\n📊 تحليل النتائج:')
  console.log(analysis)
  
  return { results, analysis }
}

// 5. تحليل النتائج وإعطاء توصيات
export const analyzeLoginResults = (results: any) => {
  const issues: string[] = []
  const solutions: string[] = []
  
  // تحليل مشاكل المستخدمين
  if (!results.users?.success) {
    issues.push('مشكلة في جدول المستخدمين')
    if (results.users?.issue === 'NO_USERS_IN_TABLE') {
      solutions.push('أضف المستخدمين إلى جدول users باستخدام SQL')
    }
  }
  
  // تحليل مشاكل تسجيل الدخول
  const failedLogins = results.loginTests?.filter((test: any) => !test.result.success) || []
  
  if (failedLogins.length > 0) {
    issues.push(`فشل في تسجيل الدخول لـ ${failedLogins.length} حساب`)
    
    failedLogins.forEach((test: any) => {
      if (test.result.issue === 'USER_NOT_IN_TABLE') {
        solutions.push(`أضف المستخدم ${test.email} إلى جدول users`)
      } else if (test.result.issue === 'INVALID_CREDENTIALS') {
        solutions.push(`تحقق من بيانات المستخدم ${test.email} في Auth`)
      } else if (test.result.issue === 'RLS_POLICY_ERROR') {
        solutions.push('أضف سياسة RLS للسماح بقراءة البيانات')
      }
    })
  }
  
  // تحديد نوع المشكلة الرئيسية
  let mainIssue = 'غير محدد'
  if (failedLogins.some((test: any) => test.result.issue === 'USER_NOT_IN_TABLE')) {
    mainIssue = 'Schema Mismatch - المستخدمون في Auth لكن ليسوا في جدول users'
  } else if (failedLogins.some((test: any) => test.result.issue === 'INVALID_CREDENTIALS')) {
    mainIssue = 'بيانات تسجيل الدخول غير صحيحة'
  } else if (failedLogins.some((test: any) => test.result.issue === 'RLS_POLICY_ERROR')) {
    mainIssue = 'مشكلة في سياسات الأمان (RLS)'
  }
  
  return {
    mainIssue,
    issues,
    solutions,
    successfulLogins: results.loginTests?.filter((test: any) => test.result.success).length || 0,
    totalTests: results.loginTests?.length || 0
  }
}
