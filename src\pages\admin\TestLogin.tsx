import { useState } from 'react';
import { supabase } from '../../lib/supabase';

const TestLogin = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('hemam2025');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [notification, setNotification] = useState<{message: string, type: 'success' | 'error'} | null>(null);

  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const testLogin = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      console.log('Attempting login with:', { email, password });
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      console.log('Login result:', { data, error });
      
      if (error) {
        setResult({
          success: false,
          error: error.message,
          details: error
        });
        showNotification(`خطأ: ${error.message}`, 'error');
      } else {
        setResult({
          success: true,
          user: data.user,
          session: data.session
        });
        showNotification('تم تسجيل الدخول بنجاح!', 'success');
        
        // تسجيل الخروج فوراً للاختبار
        await supabase.auth.signOut();
      }
    } catch (err: any) {
      console.error('Login error:', err);
      setResult({
        success: false,
        error: err.message,
        details: err
      });
      showNotification(`خطأ غير متوقع: ${err.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const testUsers = [
    { email: '<EMAIL>', name: '🧪 Test User (مستخدم اختبار جديد)' },
    { email: '<EMAIL>', name: 'معتصم العرفج' },
    { email: '<EMAIL>', name: 'سعد الدريهم' },
    { email: '<EMAIL>', name: 'أحمد الدريهم' },
    { email: '<EMAIL>', name: 'المستخدم الذي يعمل (كلمة مرور مختلفة)' }
  ];

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      {/* إشعار */}
      {notification && (
        <div className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
          notification.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`}>
          {notification.message}
        </div>
      )}

      <div className="max-w-2xl mx-auto space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-neutral-900">
            اختبار تسجيل الدخول
          </h1>
          <a
            href="/login"
            className="text-blue-600 hover:text-blue-800 text-sm underline"
          >
            العودة إلى صفحة تسجيل الدخول الرسمية
          </a>
        </div>

        <div className="space-y-4">
          <div>
            <label className="label">البريد الإلكتروني</label>
            <select
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="input-field"
            >
              {testUsers.map((user) => (
                <option key={user.email} value={user.email}>
                  {user.name} - {user.email}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="label">كلمة المرور</label>
            <input
              type="text"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="input-field"
              placeholder="كلمة المرور"
            />
          </div>

          <button
            onClick={testLogin}
            disabled={loading}
            className="btn-primary"
          >
            {loading ? 'جاري الاختبار...' : 'اختبار تسجيل الدخول'}
          </button>
        </div>

        {result && (
          <div className={`mt-6 p-4 rounded-lg ${
            result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <h3 className={`font-semibold mb-2 ${
              result.success ? 'text-green-900' : 'text-red-900'
            }`}>
              {result.success ? '✅ نجح تسجيل الدخول' : '❌ فشل تسجيل الدخول'}
            </h3>
            
            {result.error && (
              <p className="text-red-800 mb-2">
                <strong>الخطأ:</strong> {result.error}
              </p>
            )}
            
            <details className="mt-2">
              <summary className={`cursor-pointer ${
                result.success ? 'text-green-700' : 'text-red-700'
              }`}>
                عرض التفاصيل الكاملة
              </summary>
              <pre className={`mt-2 p-2 rounded text-xs overflow-auto ${
                result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </details>
          </div>
        )}

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">معلومات مفيدة:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• هذه الصفحة لاختبار تسجيل الدخول مباشرة</li>
            <li>• ستظهر رسائل الخطأ التفصيلية هنا</li>
            <li>• يتم تسجيل الخروج تلقائياً بعد النجاح</li>
            <li>• تحقق من وحدة التحكم للمزيد من التفاصيل</li>
          </ul>
        </div>
        </div>
      </div>
    </div>
  );
};

export default TestLogin;
