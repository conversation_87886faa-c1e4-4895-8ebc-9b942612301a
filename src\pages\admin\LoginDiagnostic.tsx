import { useState } from 'react';
import { runFullLoginDiagnostic, testLogin, diagnoseMismatch } from '../../lib/loginDiagnostic';

const LoginDiagnostic = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('hemam2025');

  const runDiagnostic = async () => {
    setLoading(true);
    setResults(null);
    
    try {
      const diagnosticResults = await runFullLoginDiagnostic();
      setResults(diagnosticResults);
    } catch (error) {
      console.error('خطأ في التشخيص:', error);
      setResults({
        error: 'حدث خطأ أثناء التشخيص: ' + (error as Error).message
      });
    } finally {
      setLoading(false);
    }
  };

  const testSingleLogin = async () => {
    setLoading(true);
    
    try {
      const testResult = await testLogin(testEmail, testPassword);
      setResults({
        singleTest: true,
        email: testEmail,
        result: testResult
      });
    } catch (error) {
      console.error('خطأ في اختبار تسجيل الدخول:', error);
      setResults({
        error: 'حدث خطأ أثناء اختبار تسجيل الدخول: ' + (error as Error).message
      });
    } finally {
      setLoading(false);
    }
  };

  const checkUsers = async () => {
    setLoading(true);
    
    try {
      const userResults = await diagnoseMismatch();
      setResults({
        usersOnly: true,
        result: userResults
      });
    } catch (error) {
      console.error('خطأ في فحص المستخدمين:', error);
      setResults({
        error: 'حدث خطأ أثناء فحص المستخدمين: ' + (error as Error).message
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-neutral-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-neutral-900">
              🔍 أداة تشخيص مشاكل تسجيل الدخول
            </h1>
            <a 
              href="/test-login" 
              className="text-blue-600 hover:text-blue-800 text-sm underline"
            >
              اختبار تسجيل الدخول
            </a>
          </div>

          {/* أزرار التشخيص */}
          <div className="grid md:grid-cols-3 gap-4 mb-6">
            <button
              onClick={runDiagnostic}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg font-medium disabled:opacity-50"
            >
              {loading ? '🔄 جاري التشخيص...' : '🚀 تشخيص شامل'}
            </button>

            <button
              onClick={checkUsers}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg font-medium disabled:opacity-50"
            >
              {loading ? '🔄 جاري الفحص...' : '👥 فحص المستخدمين'}
            </button>

            <div className="space-y-2">
              <div className="flex gap-2">
                <input
                  type="email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="البريد الإلكتروني"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
                />
                <input
                  type="password"
                  value={testPassword}
                  onChange={(e) => setTestPassword(e.target.value)}
                  placeholder="كلمة المرور"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
                />
              </div>
              <button
                onClick={testSingleLogin}
                disabled={loading}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded font-medium disabled:opacity-50 text-sm"
              >
                {loading ? '🔄 جاري الاختبار...' : '🧪 اختبار مستخدم واحد'}
              </button>
            </div>
          </div>

          {/* النتائج */}
          {results && (
            <div className="space-y-4">
              {results.error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="font-semibold text-red-900 mb-2">❌ خطأ</h3>
                  <p className="text-red-800">{results.error}</p>
                </div>
              )}

              {results.singleTest && (
                <div className={`border rounded-lg p-4 ${
                  results.result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}>
                  <h3 className={`font-semibold mb-2 ${
                    results.result.success ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {results.result.success ? '✅' : '❌'} اختبار تسجيل الدخول: {results.email}
                  </h3>
                  
                  {results.result.success ? (
                    <p className="text-green-800">{results.result.message}</p>
                  ) : (
                    <div className="space-y-2">
                      <p className="text-red-800">{results.result.error}</p>
                      {results.result.suggestions && (
                        <div>
                          <p className="font-medium text-red-900">الحلول المقترحة:</p>
                          <ul className="list-disc list-inside text-red-800 text-sm">
                            {results.result.suggestions.map((suggestion: string, index: number) => (
                              <li key={index}>{suggestion}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {results.usersOnly && (
                <div className={`border rounded-lg p-4 ${
                  results.result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}>
                  <h3 className={`font-semibold mb-2 ${
                    results.result.success ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {results.result.success ? '✅' : '❌'} فحص المستخدمين
                  </h3>
                  
                  {results.result.success ? (
                    <div className="text-green-800">
                      <p>عدد المستخدمين: {results.result.usersCount}</p>
                      {results.result.users && (
                        <details className="mt-2">
                          <summary className="cursor-pointer">عرض المستخدمين</summary>
                          <pre className="mt-2 text-xs bg-green-100 p-2 rounded overflow-auto">
                            {JSON.stringify(results.result.users, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <p className="text-red-800">{results.result.error}</p>
                      {results.result.suggestions && (
                        <div>
                          <p className="font-medium text-red-900">الحلول المقترحة:</p>
                          <ul className="list-disc list-inside text-red-800 text-sm">
                            {results.result.suggestions.map((suggestion: string, index: number) => (
                              <li key={index}>{suggestion}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {results.analysis && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-900 mb-3">📊 تحليل النتائج</h3>
                  
                  <div className="space-y-3 text-blue-800">
                    <div>
                      <strong>المشكلة الرئيسية:</strong> {results.analysis.mainIssue}
                    </div>
                    
                    <div>
                      <strong>نتائج الاختبارات:</strong> {results.analysis.successfulLogins}/{results.analysis.totalTests} نجح
                    </div>

                    {results.analysis.issues.length > 0 && (
                      <div>
                        <strong>المشاكل المكتشفة:</strong>
                        <ul className="list-disc list-inside mt-1">
                          {results.analysis.issues.map((issue: string, index: number) => (
                            <li key={index}>{issue}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {results.analysis.solutions.length > 0 && (
                      <div>
                        <strong>الحلول المقترحة:</strong>
                        <ul className="list-disc list-inside mt-1">
                          {results.analysis.solutions.map((solution: string, index: number) => (
                            <li key={index}>{solution}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {results.results && (
                <details className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <summary className="cursor-pointer font-medium text-gray-900">
                    عرض النتائج التفصيلية
                  </summary>
                  <pre className="mt-3 text-xs bg-gray-100 p-3 rounded overflow-auto">
                    {JSON.stringify(results.results, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          )}

          {/* معلومات الاستخدام */}
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-900 mb-2">💡 كيفية الاستخدام</h4>
            <ul className="text-yellow-800 text-sm space-y-1">
              <li>• <strong>تشخيص شامل:</strong> يفحص جميع جوانب تسجيل الدخول</li>
              <li>• <strong>فحص المستخدمين:</strong> يتحقق من وجود المستخدمين في قاعدة البيانات</li>
              <li>• <strong>اختبار مستخدم واحد:</strong> يختبر تسجيل دخول مستخدم محدد</li>
              <li>• تحقق من وحدة التحكم (F12) للمزيد من التفاصيل</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginDiagnostic;
